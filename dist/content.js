(()=>{"use strict";const e={TEXT_CONTAINERS:["p","div","span","h1","h2","h3","h4","h5","h6","li","td","th","blockquote","article","section","header","footer","main","aside","nav"],EXCLUDED_ELEMENTS:["script","style","noscript","iframe","object","embed","canvas","svg","math","code","pre","textarea","input","select","button"],PROCESSED_CLASS:"pinyin-processed",ANNOTATION_CLASS:"pinyin-annotation",TOOLTIP_CLASS:"pinyin-tooltip"},t="pinyin-container",n="pinyin-ruby",s="pinyin-tooltip",i="pinyin-bracket";new Set(["的","一","是","在","不","了","有","和","人","这","中","大","为","上","个","国","我","以","要","他","时","来","用","们","生","到","作","地","于","出","就","分","对","成","会","可","主","发","年","动","同","工","也","能","下","过","子","说","产","种","面","而","方","后","多","定","行","学","法","所"]),new class{constructor(){this.isEnabled=!1,this.currentSettings=null,this.processedNodes=new WeakSet,this.observer=null,this.isProcessing=!1,this.init()}init(){try{chrome.runtime.onMessage.addListener(this.handleMessage.bind(this)),this.setupMutationObserver(),console.log("Content script initialized")}catch(e){console.error("Failed to initialize content script:",e)}}async handleMessage(e,t,n){try{switch(e.type){case"enable_pinyin":await this.enablePinyin(e.data),n({success:!0});break;case"disable_pinyin":await this.disablePinyin(),n({success:!0});break;case"update_settings":await this.updateSettings(e.data),n({success:!0});break;default:n({success:!1,error:"Unknown message type"})}}catch(e){console.error("Error handling message:",e),n({success:!1,error:e.message})}}async enablePinyin(e){if(!this.isProcessing)try{this.isProcessing=!0,this.isEnabled=!0,this.currentSettings=e,this.injectStyles(),await this.processPageContent(),this.startObserving(),console.log("Pinyin annotation enabled")}catch(e){console.error("Failed to enable pinyin:",e),this.reportError("启用拼音标注失败",e)}finally{this.isProcessing=!1}}async disablePinyin(){try{this.isEnabled=!1,this.stopObserving(),this.removeAllAnnotations(),this.removeStyles(),console.log("Pinyin annotation disabled")}catch(e){console.error("Failed to disable pinyin:",e)}}async updateSettings(e){if(this.isEnabled)try{const t=this.currentSettings;this.currentSettings=e,(null==t?void 0:t.displayStyle)!==e.displayStyle&&(this.removeAllAnnotations(),await this.processPageContent()),console.log("Settings updated")}catch(e){console.error("Failed to update settings:",e)}}injectStyles(){if(document.getElementById("pinyin-extension-styles"))return;const e=document.createElement("style");e.id="pinyin-extension-styles",e.textContent=this.getStylesCSS(),document.head.appendChild(e)}removeStyles(){const e=document.getElementById("pinyin-extension-styles");e&&e.remove()}getStylesCSS(){return`\n      /* 拼音标注基础样式 */\n      .${t} {\n        display: inline;\n      }\n      \n      /* Ruby样式 */\n      .${n} {\n        ruby-align: center;\n      }\n      \n      .${n} rt {\n        font-size: 0.7em;\n        color: #1a73e8;\n        font-weight: normal;\n        line-height: 1;\n        user-select: none;\n      }\n      \n      /* 悬浮提示样式 */\n      .${s} {\n        position: relative;\n        cursor: help;\n        border-bottom: 1px dotted #1a73e8;\n      }\n      \n      .${s}:hover::after {\n        content: attr(data-pinyin);\n        position: absolute;\n        bottom: 100%;\n        left: 50%;\n        transform: translateX(-50%);\n        background: rgba(0, 0, 0, 0.8);\n        color: white;\n        padding: 4px 8px;\n        border-radius: 4px;\n        font-size: 12px;\n        white-space: nowrap;\n        z-index: 10000;\n        pointer-events: none;\n      }\n      \n      .${s}:hover::before {\n        content: '';\n        position: absolute;\n        bottom: 100%;\n        left: 50%;\n        transform: translateX(-50%) translateY(100%);\n        border: 4px solid transparent;\n        border-top-color: rgba(0, 0, 0, 0.8);\n        z-index: 10000;\n        pointer-events: none;\n      }\n      \n      /* 括号样式 */\n      .${i} .pinyin-bracket {\n        color: #1a73e8;\n        font-size: 0.8em;\n        margin-left: 2px;\n        user-select: none;\n      }\n      \n      /* 处理中状态 */\n      .pinyin-processing {\n        opacity: 0.6;\n      }\n      \n      /* 错误状态 */\n      .pinyin-error {\n        background-color: #ffebee;\n        border: 1px solid #f44336;\n      }\n    `}async processPageContent(){if(this.isEnabled&&!this.isProcessing)try{this.isProcessing=!0;const e=this.getTextNodes(document.body);await this.processTextNodes(e)}catch(e){console.error("Failed to process page content:",e),this.reportError("处理页面内容失败",e)}finally{this.isProcessing=!1}}getTextNodes(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_TEXT,{acceptNode:e=>{if(this.processedNodes.has(e))return NodeFilter.FILTER_REJECT;const t=e.parentElement;if(t&&this.isExcludedElement(t))return NodeFilter.FILTER_REJECT;const n=e.textContent.trim();return n&&/[\u4e00-\u9fff]/.test(n)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT}});let s;for(;s=n.nextNode();)t.push(s);return t}isExcludedElement(n){const s=n.tagName.toLowerCase();if(e.EXCLUDED_ELEMENTS.includes(s))return!0;if(n.classList.contains(t)||n.classList.contains("pinyin-annotation"))return!0;let i=n.parentElement;for(;i;){if(e.EXCLUDED_ELEMENTS.includes(i.tagName.toLowerCase()))return!0;i=i.parentElement}return!1}async processTextNodes(e){for(let t=0;t<e.length;t+=10){const n=e.slice(t,t+10);for(const e of n)await this.processTextNode(e);await new Promise(e=>setTimeout(e,0))}}async processTextNode(e){try{const n=e.textContent;if(!n||!/[\u4e00-\u9fff]/.test(n))return;this.processedNodes.add(e);const s=await this.createAnnotatedHTML(n);if(s!==n){const n=document.createElement("span");n.className=t,n.innerHTML=s,e.parentNode.replaceChild(n,e)}}catch(e){console.error("Failed to process text node:",e)}}async createAnnotatedHTML(e){const t=/[\u4e00-\u9fff]/g;if(!this.currentSettings)return e;switch(this.currentSettings.displayStyle){case"ruby":return e.replace(t,e=>{const t=this.getMockPinyin(e);return`<ruby class="${n}">${e}<rt>${t}</rt></ruby>`});case"tooltip":return e.replace(t,e=>{const t=this.getMockPinyin(e);return`<span class="${s}" data-pinyin="${t}">${e}</span>`});case"bracket":return e.replace(t,e=>{const t=this.getMockPinyin(e);return`<span class="${i}">${e}<span class="pinyin-bracket">(${t})</span></span>`});default:return e}}getMockPinyin(e){return{中:"zhōng",国:"guó",人:"rén",的:"de",一:"yī",是:"shì",在:"zài",不:"bù",了:"le",有:"yǒu"}[e]||"pin"}removeAllAnnotations(){document.querySelectorAll(`.${t}`).forEach(e=>{const t=e.textContent,n=document.createTextNode(t);e.parentNode.replaceChild(n,e)}),this.processedNodes=new WeakSet}setupMutationObserver(){this.observer=new MutationObserver(e=>{if(!this.isEnabled||this.isProcessing)return;let t=!1;e.forEach(e=>{"childList"===e.type&&e.addedNodes.forEach(e=>{(e.nodeType===Node.TEXT_NODE||e.nodeType===Node.ELEMENT_NODE&&e.textContent.trim())&&(t=!0)})}),t&&(clearTimeout(this.processTimeout),this.processTimeout=setTimeout(()=>{this.processPageContent()},500))})}startObserving(){this.observer&&this.observer.observe(document.body,{childList:!0,subtree:!0,characterData:!0})}stopObserving(){this.observer&&this.observer.disconnect(),this.processTimeout&&clearTimeout(this.processTimeout)}reportError(e,t){chrome.runtime.sendMessage({type:"error_report",data:{message:e,error:t.message,stack:t.stack,url:window.location.href,timestamp:Date.now()}})}}})();