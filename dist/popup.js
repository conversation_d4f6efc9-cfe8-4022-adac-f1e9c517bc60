(()=>{"use strict";const e="update_settings",t="ruby",n="word";new Set(["的","一","是","在","不","了","有","和","人","这","中","大","为","上","个","国","我","以","要","他","时","来","用","们","生","到","作","地","于","出","就","分","对","成","会","可","主","发","年","动","同","工","也","能","下","过","子","说","产","种","面","而","方","后","多","定","行","学","法","所"]);class s{constructor(){this.currentSettings=null,this.currentTab=null,this.isLoading=!1,this.init()}async init(){try{await this.getCurrentTab(),this.bindEventListeners(),await this.loadCurrentStatus(),console.log("Popup controller initialized")}catch(e){console.error("Failed to initialize popup controller:",e),this.showError("初始化失败，请刷新页面重试")}}async getCurrentTab(){const[e]=await chrome.tabs.query({active:!0,currentWindow:!0});this.currentTab=e}bindEventListeners(){const e=document.getElementById("main-toggle");e&&(e.addEventListener("click",this.handleToggleExtension.bind(this)),e.addEventListener("keydown",e=>{"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),this.handleToggleExtension())})),document.querySelectorAll('input[name="mode"]').forEach(e=>{e.addEventListener("change",this.handleModeChange.bind(this))});const t=document.getElementById("display-style");t&&t.addEventListener("change",this.handleStyleChange.bind(this));const n=document.getElementById("rare-words-only");n&&n.addEventListener("change",this.handleRareWordsChange.bind(this));const s=document.getElementById("more-settings");s&&s.addEventListener("click",this.handleMoreSettings.bind(this));const r=document.getElementById("help-btn");r&&r.addEventListener("click",this.handleHelp.bind(this))}async loadCurrentStatus(){try{this.setLoading(!0);const e=await this.sendMessage({type:"get_status"});if(!e.success)throw new Error(e.error||"获取状态失败");this.currentSettings=e.data,this.updateUI()}catch(e){console.error("Failed to load current status:",e),this.showError("加载状态失败")}finally{this.setLoading(!1)}}updateUI(){this.currentSettings&&(this.updateToggleState(this.currentSettings.tabEnabled||!1),this.updateModeSelection(this.currentSettings.mode||n),this.updateStyleSelection(this.currentSettings.displayStyle||t),this.updateRareWordsOption(this.currentSettings.rareWordOnly||!1),this.updateStatusText(this.currentSettings.tabEnabled||!1))}updateToggleState(e){const t=document.getElementById("main-toggle");t&&t.setAttribute("aria-checked",e.toString())}updateModeSelection(e){const t=document.querySelector(`input[name="mode"][value="${e}"]`);t&&(t.checked=!0)}updateStyleSelection(e){const t=document.getElementById("display-style");t&&(t.value=e)}updateRareWordsOption(e){const t=document.getElementById("rare-words-only");t&&(t.checked=e)}updateStatusText(e){const t=document.getElementById("status-text");t&&(t.textContent=e?"已启用":"已禁用",t.style.color=e?"var(--success-color)":"var(--text-secondary)")}async handleToggleExtension(){if(!this.isLoading)try{this.setLoading(!0);const e=await this.sendMessage({type:"toggle_extension"});if(!e.success)throw new Error(e.error||"切换失败");{const t=e.data.enabled;this.updateToggleState(t),this.updateStatusText(t),this.currentSettings&&(this.currentSettings.tabEnabled=t)}}catch(e){console.error("Failed to toggle extension:",e),this.showError("操作失败，请重试")}finally{this.setLoading(!1)}}async handleModeChange(e){const t=e.target.value;try{const e=await this.sendMessage({type:"change_mode",data:{mode:t}});if(!e.success)throw new Error(e.error||"模式切换失败");this.currentSettings=e.data,console.log(`Mode changed to: ${t}`)}catch(e){var s;console.error("Failed to change mode:",e),this.showError("模式切换失败"),this.updateModeSelection((null===(s=this.currentSettings)||void 0===s?void 0:s.mode)||n)}}async handleStyleChange(n){const s=n.target.value;try{const t={...this.currentSettings,displayStyle:s},n=await this.sendMessage({type:e,data:t});if(!n.success)throw new Error(n.error||"样式切换失败");this.currentSettings.displayStyle=s,console.log(`Display style changed to: ${s}`)}catch(e){var r;console.error("Failed to change display style:",e),this.showError("样式切换失败"),this.updateStyleSelection((null===(r=this.currentSettings)||void 0===r?void 0:r.displayStyle)||t)}}async handleRareWordsChange(t){const n=t.target.checked;try{const t={...this.currentSettings,rareWordOnly:n},s=await this.sendMessage({type:e,data:t});if(!s.success)throw new Error(s.error||"设置更新失败");this.currentSettings.rareWordOnly=n,console.log(`Rare words only: ${n}`)}catch(e){var s;console.error("Failed to update rare words setting:",e),this.showError("设置更新失败"),this.updateRareWordsOption((null===(s=this.currentSettings)||void 0===s?void 0:s.rareWordOnly)||!1)}}handleMoreSettings(){chrome.runtime.openOptionsPage()}handleHelp(){const e=chrome.runtime.getURL("help.html");chrome.tabs.create({url:e})}async sendMessage(e){return new Promise(t=>{chrome.runtime.sendMessage(e,e=>{chrome.runtime.lastError?t({success:!1,error:chrome.runtime.lastError.message}):t(e||{success:!1,error:"No response"})})})}setLoading(e){this.isLoading=e;const t=document.getElementById("loading-overlay");t&&(e?t.removeAttribute("hidden"):t.setAttribute("hidden","")),document.querySelectorAll("button, input, select").forEach(t=>{t.disabled=e})}showError(e){const t=document.getElementById("error-toast"),n=document.getElementById("error-message");t&&n&&(n.textContent=e,t.removeAttribute("hidden"),setTimeout(()=>{t.setAttribute("hidden","")},3e3))}}document.addEventListener("DOMContentLoaded",()=>{new s})})();