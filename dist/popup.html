<!doctype html><html lang="zh-CN"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>拼音标注插件</title><link rel="stylesheet" href="popup.css"><script defer="defer" src="popup.js"></script></head><body><div class="popup-container"><header class="popup-header"><h1 class="popup-title"><img src="../icons/icon-32.png" alt="拼音标注" class="title-icon"> 拼音标注插件</h1></header><main class="popup-main"><section class="control-section"><div class="toggle-container"><label class="toggle-label" for="main-toggle"><span class="toggle-text">启用拼音标注</span><div class="toggle-switch" id="main-toggle" role="switch" aria-checked="false"><div class="toggle-slider"></div></div></label></div><div class="status-indicator" id="status-indicator"><span class="status-text" id="status-text">已禁用</span></div></section><section class="mode-section"><h3 class="section-title">标注模式</h3><div class="radio-group"><label class="radio-label"><input type="radio" name="mode" value="char" class="radio-input"> <span class="radio-custom"></span> <span class="radio-text">逐字标注</span></label> <label class="radio-label"><input type="radio" name="mode" value="word" class="radio-input" checked="checked"> <span class="radio-custom"></span> <span class="radio-text">逐词标注</span></label></div></section><section class="style-section"><h3 class="section-title">显示样式</h3><div class="select-container"><select id="display-style" class="select-dropdown"><option value="ruby">上方注音</option><option value="tooltip">悬浮提示</option><option value="bracket">括号拼音</option></select></div></section><section class="quick-settings"><div class="checkbox-container"><label class="checkbox-label"><input type="checkbox" id="rare-words-only" class="checkbox-input"> <span class="checkbox-custom"></span> <span class="checkbox-text">仅标注生僻字</span></label></div></section></main><footer class="popup-footer"><div class="button-group"><button class="btn btn-secondary" id="more-settings"><span class="btn-icon">⚙️</span> 更多设置</button> <button class="btn btn-secondary" id="help-btn"><span class="btn-icon">❓</span> 帮助</button></div></footer></div><div class="loading-overlay" id="loading-overlay" hidden><div class="loading-spinner"></div><div class="loading-text">处理中...</div></div><div class="error-toast" id="error-toast" hidden><div class="error-content"><span class="error-icon">⚠️</span> <span class="error-message" id="error-message"></span></div></div><script src="popup.js"></script></body></html>