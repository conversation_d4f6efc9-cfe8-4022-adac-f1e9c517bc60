(()=>{"use strict";const e="enable_pinyin",t="update_settings",a="storage_error",s="debug",r="info",o="warn",i="error",n={enabled:!1,mode:"word",displayStyle:"ruby",toneType:"symbol",scope:"all",rareWordOnly:!1,autoEnable:!1,excludeDomains:[],fontSize:"medium",colorTheme:"blue",wordFrequencyThreshold:5},l="settings",c="cache",g="stats";new Set(["的","一","是","在","不","了","有","和","人","这","中","大","为","上","个","国","我","以","要","他","时","来","用","们","生","到","作","地","于","出","就","分","对","成","会","可","主","发","年","动","同","工","也","能","下","过","子","说","产","种","面","而","方","后","多","定","行","学","法","所"]);class d{constructor(e="Unknown",t=r){this.module=e,this.level=t,this.levels=[s,r,o,i],this.colors={[s]:"#6B7280",[r]:"#3B82F6",[o]:"#F59E0B",[i]:"#EF4444"}}shouldLog(e){const t=this.levels.indexOf(this.level);return this.levels.indexOf(e)>=t}log(e,t,a=null){if(!this.shouldLog(e))return;const s=`[${(new Date).toISOString()}] [${this.module}] ${t}`,r=this.getConsoleMethod(e),o=this.colors[e];a?r(`%c${s}`,`color: ${o}; font-weight: bold;`,a):r(`%c${s}`,`color: ${o}; font-weight: bold;`),this.saveLog(e,t,a)}getConsoleMethod(e){switch(e){case s:return console.debug;case r:return console.info;case o:return console.warn;case i:return console.error;default:return console.log}}async saveLog(e,t,a){try{var s;if(e!==o&&e!==i)return;const r={timestamp:Date.now(),level:e,module:this.module,message:t,data:a?JSON.stringify(a):null,userAgent:navigator.userAgent,url:(null===(s=window.location)||void 0===s?void 0:s.href)||"unknown"},n=(await chrome.storage.local.get("logs")).logs||[];n.push(r),n.length>100&&n.splice(0,n.length-100),await chrome.storage.local.set({logs:n})}catch(e){console.error("Failed to save log:",e)}}debug(e,t){this.log(s,e,t)}info(e,t){this.log(r,e,t)}warn(e,t){this.log(o,e,t)}error(e,t){this.log(i,e,t)}setLevel(e){this.levels.includes(e)?(this.level=e,this.info(`Log level changed to ${e}`)):this.warn(`Invalid log level: ${e}`)}createSubLogger(e){return new d(`${this.module}:${e}`,this.level)}static async getSavedLogs(){try{return(await chrome.storage.local.get("logs")).logs||[]}catch(e){return console.error("Failed to get saved logs:",e),[]}}static async clearSavedLogs(){try{await chrome.storage.local.remove("logs"),console.info("Saved logs cleared")}catch(e){console.error("Failed to clear saved logs:",e)}}static async exportLogs(){try{const e=await d.getSavedLogs(),t={exportTime:(new Date).toISOString(),version:"1.0.0",logs:e.map(e=>({...e,timestamp:new Date(e.timestamp).toISOString()}))};return JSON.stringify(t,null,2)}catch(e){return console.error("Failed to export logs:",e),"{}"}}time(e){const t=performance.now();return this.debug(`Timer started: ${e}`),()=>{const a=performance.now()-t;return this.debug(`Timer ended: ${e}`,{duration:`${a.toFixed(2)}ms`}),a}}async timeAsync(e,t){const a=this.time(t);try{const t=await e();return a(),t}catch(e){throw a(),this.error(`Error in timed function ${t}`,e),e}}memory(e="Memory Usage"){if(performance.memory){const t={used:Math.round(performance.memory.usedJSHeapSize/1024/1024),total:Math.round(performance.memory.totalJSHeapSize/1024/1024),limit:Math.round(performance.memory.jsHeapSizeLimit/1024/1024)};this.debug(e,{usedMB:t.used,totalMB:t.total,limitMB:t.limit})}else this.debug(`${e}: Memory API not available`)}network(e,t="GET",a,r){const o=a>=400?i:s;this.log(o,`Network ${t} ${e}`,{status:a,duration:r?`${r}ms`:void 0})}}class h{constructor(){this.logger=new d("Storage")}async getSettings(){try{const e=(await chrome.storage.sync.get(l))[l];if(!e)return this.logger.info("No settings found, returning defaults"),{...n};const t={...n,...e};return this.logger.debug("Settings retrieved",t),t}catch(e){throw this.logger.error("Failed to get settings",e),new Error(`${a}: Failed to get settings`)}}async saveSettings(e){try{const t=this.validateSettings(e);await chrome.storage.sync.set({[l]:t}),this.logger.info("Settings saved successfully",t)}catch(e){throw this.logger.error("Failed to save settings",e),new Error(`${a}: Failed to save settings`)}}async resetSettings(){try{await this.saveSettings(n),this.logger.info("Settings reset to defaults")}catch(e){throw this.logger.error("Failed to reset settings",e),e}}async getCache(e){try{const t=`${c}.${e}`,a=(await chrome.storage.local.get(t))[t];return a?a.expireTime&&Date.now()>a.expireTime?(await this.removeCache(e),null):(this.logger.debug(`Cache hit for key: ${e}`),a.data):null}catch(t){return this.logger.error(`Failed to get cache for key: ${e}`,t),null}}async setCache(e,t,a=36e5){try{const s=`${c}.${e}`,r={data:t,createTime:Date.now(),expireTime:a>0?Date.now()+a:null};await chrome.storage.local.set({[s]:r}),this.logger.debug(`Cache set for key: ${e}`)}catch(t){this.logger.error(`Failed to set cache for key: ${e}`,t)}}async removeCache(e){try{const t=`${c}.${e}`;await chrome.storage.local.remove(t),this.logger.debug(`Cache removed for key: ${e}`)}catch(t){this.logger.error(`Failed to remove cache for key: ${e}`,t)}}async clearCache(){try{const e=await chrome.storage.local.get(null),t=Object.keys(e).filter(e=>e.startsWith(c));t.length>0&&(await chrome.storage.local.remove(t),this.logger.info(`Cleared ${t.length} cache entries`))}catch(e){throw this.logger.error("Failed to clear cache",e),new Error(`${a}: Failed to clear cache`)}}async getCacheSize(){try{const e=await chrome.storage.local.get(null),t=Object.entries(e).filter(([e])=>e.startsWith(c)),a=JSON.stringify(t).length;return{totalSize:a,entryCount:t.length,formattedSize:this.formatBytes(a)}}catch(e){return this.logger.error("Failed to get cache size",e),{totalSize:0,entryCount:0,formattedSize:"0 B"}}}async getStats(){try{return(await chrome.storage.local.get(g))[g]||await this.initStats()}catch(e){return this.logger.error("Failed to get stats",e),await this.initStats()}}async initStats(){const e={totalUsage:0,lastUsed:null,installTime:Date.now(),charactersProcessed:0,wordsProcessed:0,errorsOccurred:0,favoriteWords:[]};try{return await chrome.storage.local.set({[g]:e}),this.logger.info("Stats initialized"),e}catch(t){return this.logger.error("Failed to initialize stats",t),e}}async updateStats(e){try{const t={...await this.getStats(),...e};await chrome.storage.local.set({[g]:t}),this.logger.debug("Stats updated",e)}catch(e){this.logger.error("Failed to update stats",e)}}async incrementUsage(){try{const e=await this.getStats();await this.updateStats({totalUsage:e.totalUsage+1,lastUsed:Date.now()})}catch(e){this.logger.error("Failed to increment usage",e)}}async exportData(){try{const[e,t]=await Promise.all([chrome.storage.sync.get(null),chrome.storage.local.get(null)]),a={version:"1.0.0",exportTime:Date.now(),settings:e[l]||n,stats:t[g]||{}};return this.logger.info("Data exported successfully"),a}catch(e){throw this.logger.error("Failed to export data",e),new Error(`${a}: Failed to export data`)}}async importData(e){try{if(!e||"object"!=typeof e)throw new Error("Invalid import data format");e.version&&"1.0.0"!==e.version&&this.logger.warn("Import data version mismatch",{expected:"1.0.0",actual:e.version}),e.settings&&await this.saveSettings(e.settings),e.stats&&await chrome.storage.local.set({[g]:e.stats}),this.logger.info("Data imported successfully")}catch(e){throw this.logger.error("Failed to import data",e),new Error(`${a}: Failed to import data`)}}validateSettings(e){const t={...n};return Object.keys(n).forEach(a=>{if(e.hasOwnProperty(a)){const s=e[a],r=n[a];typeof s==typeof r?Array.isArray(r)?t[a]=Array.isArray(s)?s:r:t[a]=s:(this.logger.warn(`Invalid type for setting ${a}, using default`),t[a]=r)}}),t}formatBytes(e){if(0===e)return"0 B";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB"][t]}onStorageChanged(e){chrome.storage.onChanged.addListener((t,a)=>{this.logger.debug("Storage changed",{changes:t,namespace:a}),e(t,a)})}}new class{constructor(){this.storageManager=new h,this.logger=new d("Background"),this.activeTabStates=new Map,this.init()}async init(){try{chrome.runtime.onInstalled.addListener(this.handleInstalled.bind(this)),chrome.runtime.onMessage.addListener(this.handleMessage.bind(this)),chrome.tabs.onUpdated.addListener(this.handleTabUpdated.bind(this)),chrome.tabs.onActivated.addListener(this.handleTabActivated.bind(this)),chrome.tabs.onRemoved.addListener(this.handleTabRemoved.bind(this)),await this.initializeSettings(),this.logger.info("Background service initialized successfully")}catch(e){this.logger.error("Failed to initialize background service",e)}}async handleInstalled(e){try{"install"===e.reason?(this.logger.info("Extension installed"),await this.onFirstInstall()):"update"===e.reason&&(this.logger.info("Extension updated",{previousVersion:e.previousVersion}),await this.onUpdate(e.previousVersion))}catch(e){this.logger.error("Error handling installation",e)}}async onFirstInstall(){await this.storageManager.saveSettings({enabled:!1,mode:"word",displayStyle:"ruby",toneType:"symbol",scope:"all",rareWordOnly:!1,autoEnable:!1,excludeDomains:[],fontSize:"medium",colorTheme:"blue",wordFrequencyThreshold:5}),await this.storageManager.initStats(),this.logger.info("First install initialization completed")}async onUpdate(e){this.logger.info(`Updated from version ${e}`)}async initializeSettings(){await this.storageManager.getSettings()||await this.onFirstInstall()}async handleMessage(e,a,s){var r,o,i,n,l;try{var c;switch(this.logger.debug("Received message",{type:e.type,sender:null===(c=a.tab)||void 0===c?void 0:c.id}),e.type){case"get_status":return await this.handleGetStatus(null===(r=a.tab)||void 0===r?void 0:r.id);case"toggle_extension":return await this.handleToggleExtension(null===(o=a.tab)||void 0===o?void 0:o.id);case"change_mode":return await this.handleChangeMode(e.data,null===(i=a.tab)||void 0===i?void 0:i.id);case t:return await this.handleUpdateSettings(e.data);case"status_update":return await this.handleStatusUpdate(e.data,null===(n=a.tab)||void 0===n?void 0:n.id);case"error_report":return await this.handleErrorReport(e.data,null===(l=a.tab)||void 0===l?void 0:l.id);default:return this.logger.warn("Unknown message type",{type:e.type}),{success:!1,error:"Unknown message type"}}}catch(e){return this.logger.error("Error handling message",e),{success:!1,error:e.message}}}async handleGetStatus(e){return{success:!0,data:{...await this.storageManager.getSettings(),tabEnabled:(this.activeTabStates.get(e)||{enabled:!1}).enabled,tabId:e}}}async handleToggleExtension(t){const a=this.activeTabStates.get(t)||{enabled:!1},s=!a.enabled;this.activeTabStates.set(t,{...a,enabled:s});const r=s?e:"disable_pinyin",o=await this.storageManager.getSettings();try{return await chrome.tabs.sendMessage(t,{type:r,data:o}),await this.updateBadge(t,s),s&&await this.storageManager.incrementUsage(),this.logger.info(`Extension ${s?"enabled":"disabled"} for tab ${t}`),{success:!0,data:{enabled:s}}}catch(e){return this.logger.error("Failed to toggle extension",e),{success:!1,error:"Failed to communicate with content script"}}}async handleChangeMode(e,a){const s=await this.storageManager.getSettings();s.mode=e.mode,await this.storageManager.saveSettings(s);const r=this.activeTabStates.get(a);if(null!=r&&r.enabled)try{await chrome.tabs.sendMessage(a,{type:t,data:s})}catch(e){this.logger.warn("Failed to update content script settings",e)}return{success:!0,data:s}}async handleUpdateSettings(e){await this.storageManager.saveSettings(e);for(const[a,s]of this.activeTabStates.entries())if(s.enabled)try{await chrome.tabs.sendMessage(a,{type:t,data:e})}catch(e){this.logger.warn(`Failed to update settings for tab ${a}`,e)}return{success:!0}}async handleStatusUpdate(e,t){const a=this.activeTabStates.get(t)||{};return this.activeTabStates.set(t,{...a,...e}),{success:!0}}async handleErrorReport(e,t){return this.logger.error("Error reported from content script",{tabId:t,error:e}),{success:!0}}async handleTabUpdated(t,a,s){if("complete"===a.status&&s.url){const a=await this.storageManager.getSettings();if(a.autoEnable&&!this.isExcludedDomain(s.url,a.excludeDomains)){this.activeTabStates.set(t,{enabled:!0});try{await chrome.tabs.sendMessage(t,{type:e,data:a}),await this.updateBadge(t,!0)}catch(e){this.logger.warn(`Failed to auto-enable for tab ${t}`,e)}}}}async handleTabActivated(e){const t=this.activeTabStates.get(e.tabId);t&&await this.updateBadge(e.tabId,t.enabled)}handleTabRemoved(e){this.activeTabStates.delete(e),this.logger.debug(`Cleaned up state for closed tab ${e}`)}async updateBadge(e,t){try{await chrome.action.setBadgeText({text:t?"开":"",tabId:e}),await chrome.action.setBadgeBackgroundColor({color:t?"#4CAF50":"#9E9E9E",tabId:e})}catch(e){this.logger.warn("Failed to update badge",e)}}isExcludedDomain(e,t){try{const a=new URL(e).hostname;return t.some(e=>a===e||a.endsWith("."+e))}catch(e){return!1}}}})();