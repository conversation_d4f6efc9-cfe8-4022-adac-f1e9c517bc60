(()=>{"use strict";const e={enabled:!1,mode:"word",displayStyle:"ruby",toneType:"symbol",scope:"all",rareWordOnly:!1,autoEnable:!1,excludeDomains:[],fontSize:"medium",colorTheme:"blue",wordFrequencyThreshold:5};new Set(["的","一","是","在","不","了","有","和","人","这","中","大","为","上","个","国","我","以","要","他","时","来","用","们","生","到","作","地","于","出","就","分","对","成","会","可","主","发","年","动","同","工","也","能","下","过","子","说","产","种","面","而","方","后","多","定","行","学","法","所"]);class t{constructor(){this.currentSettings=null,this.isLoading=!1,this.hasUnsavedChanges=!1,this.init()}async init(){try{this.bindEventListeners(),await this.loadSettings(),this.bindUnloadEvents(),console.log("Options controller initialized")}catch(e){console.error("Failed to initialize options controller:",e),this.showToast("初始化失败，请刷新页面重试","error")}}bindEventListeners(){const e=document.getElementById("save-settings");e&&e.addEventListener("click",this.handleSaveSettings.bind(this));const t=document.getElementById("reset-settings");t&&t.addEventListener("click",this.handleResetSettings.bind(this));const s=document.getElementById("export-settings");s&&s.addEventListener("click",this.handleExportSettings.bind(this));const n=document.getElementById("import-settings");n&&n.addEventListener("click",this.handleImportSettings.bind(this));const i=document.getElementById("clear-cache");i&&i.addEventListener("click",this.handleClearCache.bind(this));const a=document.getElementById("add-exclude-site");a&&a.addEventListener("click",this.handleAddExcludeSite.bind(this));const o=document.getElementById("help-link");o&&o.addEventListener("click",this.handleHelpLink.bind(this)),this.bindFormChangeListeners()}bindFormChangeListeners(){document.querySelectorAll("input, select, textarea").forEach(e=>{e.addEventListener("change",()=>{this.hasUnsavedChanges=!0,this.updateSaveButtonState()})})}bindUnloadEvents(){window.addEventListener("beforeunload",e=>{this.hasUnsavedChanges&&(e.preventDefault(),e.returnValue="您有未保存的更改，确定要离开吗？")})}async loadSettings(){try{this.setLoading(!0);const e=await this.sendMessage({type:"get_status"});if(!e.success)throw new Error(e.error||"加载设置失败");this.currentSettings=e.data,this.updateUI(),this.hasUnsavedChanges=!1,this.updateSaveButtonState()}catch(e){console.error("Failed to load settings:",e),this.showToast("加载设置失败","error")}finally{this.setLoading(!1)}}updateUI(){this.currentSettings&&(this.updateCheckbox("auto-enable",this.currentSettings.autoEnable),this.updateCheckbox("rare-words-only",this.currentSettings.rareWordOnly),this.updateSelect("annotation-scope",this.currentSettings.scope),this.updateSelect("display-style",this.currentSettings.displayStyle),this.updateSelect("pinyin-format",this.currentSettings.toneType),this.updateSelect("font-size",this.currentSettings.fontSize),this.updateSelect("color-theme",this.currentSettings.colorTheme),this.updateRange("word-frequency-threshold",this.currentSettings.wordFrequencyThreshold),this.updateExcludeList(this.currentSettings.excludeDomains))}updateCheckbox(e,t){const s=document.getElementById(e);s&&(s.checked=t)}updateSelect(e,t){const s=document.getElementById(e);s&&(s.value=t)}updateRange(e,t){const s=document.getElementById(e);s&&(s.value=t)}updateExcludeList(e){const t=document.getElementById("exclude-list");t&&(t.innerHTML="",e.forEach((e,s)=>{const n=document.createElement("li");n.className="exclude-item",n.innerHTML=`\n        <span class="exclude-domain">${e}</span>\n        <button class="btn btn-small btn-danger" data-index="${s}">删除</button>\n      `,n.querySelector("button").addEventListener("click",()=>{this.handleRemoveExcludeSite(s)}),t.appendChild(n)}))}async handleSaveSettings(){if(!this.isLoading)try{this.setLoading(!0);const e=this.collectFormData(),t=await this.sendMessage({type:"update_settings",data:e});if(!t.success)throw new Error(t.error||"保存设置失败");this.currentSettings=e,this.hasUnsavedChanges=!1,this.updateSaveButtonState(),this.showToast("设置保存成功","success")}catch(e){console.error("Failed to save settings:",e),this.showToast("保存设置失败","error")}finally{this.setLoading(!1)}}collectFormData(){return{...this.currentSettings,autoEnable:this.getCheckboxValue("auto-enable"),rareWordOnly:this.getCheckboxValue("rare-words-only"),scope:this.getSelectValue("annotation-scope"),displayStyle:this.getSelectValue("display-style"),toneType:this.getSelectValue("pinyin-format"),fontSize:this.getSelectValue("font-size"),colorTheme:this.getSelectValue("color-theme"),wordFrequencyThreshold:parseInt(this.getRangeValue("word-frequency-threshold")),excludeDomains:this.currentSettings.excludeDomains||[]}}getCheckboxValue(e){const t=document.getElementById(e);return!!t&&t.checked}getSelectValue(e){const t=document.getElementById(e);return t?t.value:""}getRangeValue(e){const t=document.getElementById(e);return t?t.value:0}async handleResetSettings(){if(await this.showConfirmDialog("重置设置","确定要重置所有设置为默认值吗？此操作不可撤销。"))try{this.setLoading(!0);const t=await this.sendMessage({type:"reset_settings"});if(!t.success)throw new Error(t.error||"重置设置失败");this.currentSettings=e,this.updateUI(),this.hasUnsavedChanges=!1,this.updateSaveButtonState(),this.showToast("设置已重置为默认值","success")}catch(e){console.error("Failed to reset settings:",e),this.showToast("重置设置失败","error")}finally{this.setLoading(!1)}}async handleExportSettings(){try{const e=JSON.stringify(this.currentSettings,null,2),t=new Blob([e],{type:"application/json"}),s=URL.createObjectURL(t),n=document.createElement("a");n.href=s,n.download=`pinyin-extension-settings-${(new Date).toISOString().split("T")[0]}.json`,n.click(),URL.revokeObjectURL(s),this.showToast("设置导出成功","success")}catch(e){console.error("Failed to export settings:",e),this.showToast("导出设置失败","error")}}handleImportSettings(){const t=document.createElement("input");t.type="file",t.accept=".json",t.onchange=async t=>{const s=t.target.files[0];if(s)try{const t=await s.text(),n=JSON.parse(t);if("object"!=typeof n)throw new Error("无效的设置文件格式");this.currentSettings={...e,...n},this.updateUI(),this.hasUnsavedChanges=!0,this.updateSaveButtonState(),this.showToast("设置导入成功，请保存设置","success")}catch(e){console.error("Failed to import settings:",e),this.showToast("导入设置失败："+e.message,"error")}},t.click()}async handleClearCache(){if(await this.showConfirmDialog("清理缓存","确定要清理所有缓存数据吗？这将删除拼音转换和分词缓存。"))try{this.showToast("缓存清理成功","success")}catch(e){console.error("Failed to clear cache:",e),this.showToast("清理缓存失败","error")}}handleAddExcludeSite(){const e=document.getElementById("exclude-site-input");if(!e)return;const t=e.value.trim();t?/^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(t)?this.currentSettings.excludeDomains.includes(t)?this.showToast("该域名已在排除列表中","warning"):(this.currentSettings.excludeDomains.push(t),this.updateExcludeList(this.currentSettings.excludeDomains),e.value="",this.hasUnsavedChanges=!0,this.updateSaveButtonState()):this.showToast("域名格式不正确","warning"):this.showToast("请输入有效的域名","warning")}handleRemoveExcludeSite(e){this.currentSettings.excludeDomains.splice(e,1),this.updateExcludeList(this.currentSettings.excludeDomains),this.hasUnsavedChanges=!0,this.updateSaveButtonState()}handleHelpLink(){const e=chrome.runtime.getURL("help.html");chrome.tabs.create({url:e})}updateSaveButtonState(){const e=document.getElementById("save-settings");e&&(e.disabled=!this.hasUnsavedChanges||this.isLoading,e.textContent=this.hasUnsavedChanges?"保存设置 *":"保存设置")}setLoading(e){this.isLoading=e,document.querySelectorAll("button, input, select, textarea").forEach(t=>{t.disabled=e})}async showConfirmDialog(e,t){return new Promise(s=>{const n=document.getElementById("modal-overlay"),i=document.getElementById("modal-title"),a=document.getElementById("modal-message"),o=document.getElementById("modal-confirm"),r=document.getElementById("modal-cancel");if(!(n&&i&&a&&o&&r))return void s(!1);i.textContent=e,a.textContent=t,n.removeAttribute("hidden");const c=()=>{n.setAttribute("hidden",""),l(),s(!0)},d=()=>{n.setAttribute("hidden",""),l(),s(!1)},l=()=>{o.removeEventListener("click",c),r.removeEventListener("click",d)};o.addEventListener("click",c),r.addEventListener("click",d)})}showToast(e,t="info"){const s=document.getElementById("toast"),n=document.getElementById("toast-icon"),i=document.getElementById("toast-message");if(!s||!n||!i)return;const a={success:"✓",error:"✗",warning:"⚠",info:"ℹ"};n.textContent=a[t]||a.info,i.textContent=e,s.className=`toast toast-${t}`,s.removeAttribute("hidden"),setTimeout(()=>{s.setAttribute("hidden","")},3e3)}async sendMessage(e){return new Promise(t=>{chrome.runtime.sendMessage(e,e=>{chrome.runtime.lastError?t({success:!1,error:chrome.runtime.lastError.message}):t(e||{success:!1,error:"No response"})})})}}document.addEventListener("DOMContentLoaded",()=>{new t})})();