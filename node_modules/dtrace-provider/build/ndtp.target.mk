# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := ndtp
### Rules for action "build_ndtp":
quiet_cmd_binding_gyp_ndtp_target_build_ndtp = ACTION binding_gyp_ndtp_target_build_ndtp $@
cmd_binding_gyp_ndtp_target_build_ndtp = LD_LIBRARY_PATH=$(builddir)/lib.host:$(builddir)/lib.target:$$LD_LIBRARY_PATH; export LD_LIBRARY_PATH; cd $(srcdir)/.; bash build.sh

.: obj := $(abs_obj)
.: builddir := $(abs_builddir)
.: export BUILT_FRAMEWORKS_DIR := ${abs_builddir}
.: export BUILT_PRODUCTS_DIR := ${abs_builddir}
.: export CONFIGURATION := ${BUILDTYPE}
.: export PRODUCT_NAME := ndtp
.: export SDKROOT := /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk
.: export SRCROOT := ${abs_srcdir}/
.: export SOURCE_ROOT := ${SRCROOT}
.: export TARGET_BUILD_DIR := ${abs_builddir}
.: export TEMP_DIR := ${TMPDIR}
.: export XCODE_VERSION_ACTUAL := 1640
.: TOOLSET := $(TOOLSET)
.: $(srcdir)/. FORCE_DO_CMD
	$(call do_cmd,binding_gyp_ndtp_target_build_ndtp)

all_deps += .
action_binding_gyp_ndtp_target_build_ndtp_outputs := .


### Rules for final target.
# Build our special outputs first.
$(obj).target/ndtp.stamp: | $(action_binding_gyp_ndtp_target_build_ndtp_outputs)

# Preserve order dependency of special output on deps.
$(action_binding_gyp_ndtp_target_build_ndtp_outputs): | 

$(obj).target/ndtp.stamp: TOOLSET := $(TOOLSET)
$(obj).target/ndtp.stamp:  FORCE_DO_CMD
	$(call do_cmd,touch)

all_deps += $(obj).target/ndtp.stamp
# Add target alias
.PHONY: ndtp
ndtp: $(obj).target/ndtp.stamp

# Add target alias to "all" target.
.PHONY: all
all: ndtp

